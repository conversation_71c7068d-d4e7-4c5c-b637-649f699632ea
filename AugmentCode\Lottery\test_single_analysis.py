#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试单次分析功能的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lottery_final import LotteryAnalyzer

def test_single_analysis():
    """
    测试单次分析功能
    """
    print("=" * 60)
    print("测试单次分析功能")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzer("lottery_data_all.xlsx")
    
    # 设置测试参数
    analyzer.lottery_type = "SSQ"  # 使用双色球进行测试
    analyzer.operation_mode = "analysis"
    analyzer.analysis_mode = "single"
    
    # 读取数据
    print("正在读取数据...")
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False
    
    # 设置数据
    if analyzer.lottery_type == "SSQ":
        analyzer.data = analyzer.ssqhistory_allout
    else:
        analyzer.data = analyzer.dlthistory_allout
    
    print(f"数据读取成功，共 {len(analyzer.data)} 行数据")
    print(f"期号范围：{analyzer.data.iloc[0, 0]} - {analyzer.data.iloc[-1, 0]}")
    
    # 模拟用户输入一个测试期号
    # 选择一个中间的期号进行测试，确保有足够的前期数据和后续6期数据
    test_period = analyzer.data.iloc[len(analyzer.data)//2, 0]
    print(f"\n使用测试期号：{test_period}")
    
    # 手动设置目标期号进行测试
    target_row = None
    for i, row in analyzer.data.iterrows():
        if row.iloc[0] == test_period:
            target_row = i
            break
    
    if target_row is None or target_row + 6 >= len(analyzer.data):
        print("测试期号不合适，选择其他期号")
        test_period = analyzer.data.iloc[100, 0]  # 选择第100行的期号
        for i, row in analyzer.data.iterrows():
            if row.iloc[0] == test_period:
                target_row = i
                break
    
    print(f"目标行：{target_row}，期号：{test_period}")
    
    # 检查是否有足够的后续数据
    if target_row + 6 >= len(analyzer.data):
        print(f"期号 {test_period} 后续数据不足6期，无法进行测试")
        return False
    
    # 使用目标期号之前的数据作为训练数据
    train_data = analyzer.data.iloc[:target_row].copy()
    
    if len(train_data) < 10:
        print("训练数据不足，无法进行测试")
        return False
    
    print(f"训练数据：{len(train_data)} 行")
    
    # 获取目标期号及后续5期的实际号码（共6期作为标准）
    standard_periods = []
    for i in range(6):
        period_row = target_row + i
        period_number = analyzer.data.iloc[period_row, 0]
        if analyzer.lottery_type == "SSQ":
            actual_numbers = analyzer.data.iloc[period_row, 1:8].tolist()
        else:
            actual_numbers = analyzer.data.iloc[period_row, 1:8].tolist()
        standard_periods.append({
            'period': period_number,
            'numbers': actual_numbers
        })
    
    print(f"\n后续6期数据准备完成")
    
    # 进行两注号码预测
    print("\n开始预测...")
    multi_prediction = analyzer.predict_numbers(train_data, "multi_bayesian")
    full_prediction = analyzer.predict_numbers(train_data, "full_bayesian")
    
    # 进行多注号码预测
    red_probs, blue_probs = analyzer.calculate_multi_bayesian_probs_for_data(train_data)
    
    if analyzer.lottery_type == "SSQ":
        multi_red = [int(x) for x in multi_prediction[:6]]
        multi_blue = [int(x) for x in multi_prediction[6:7]]
        full_red = [int(x) for x in full_prediction[:6]]
        full_blue = [int(x) for x in full_prediction[6:7]]
        
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
        multi_pred_red = [int(ball) for ball, _ in red_sorted[:7]]  # SSQ: 7个红球
        multi_pred_blue = [int(ball) for ball, _ in blue_sorted[:2]]  # SSQ: 2个蓝球
    else:
        multi_red = [int(x) for x in multi_prediction[:5]]
        multi_blue = [int(x) for x in multi_prediction[5:7]]
        full_red = [int(x) for x in full_prediction[:5]]
        full_blue = [int(x) for x in full_prediction[5:7]]
        
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
        multi_pred_red = [int(ball) for ball, _ in red_sorted[:6]]  # DLT: 6个红球
        multi_pred_blue = [int(ball) for ball, _ in blue_sorted[:3]]  # DLT: 3个蓝球
    
    # 打印预测结果
    print(f"\n两注号码预测结果：")
    print(f"多条件贝叶斯预测：红球 {multi_red}，蓝球 {multi_blue}")
    print(f"全条件贝叶斯预测：红球 {full_red}，蓝球 {full_blue}")
    
    print(f"\n多注号码预测结果：")
    formatted_multi_pred = analyzer.format_multi_prediction_numbers(multi_pred_red, multi_pred_blue)
    print(f"{formatted_multi_pred}")
    
    # 测试新的比对功能
    print("\n" + "=" * 60)
    print("测试新的比对功能")
    print("=" * 60)
    
    # 与后续6期进行比对 - 多条件贝叶斯
    print(f"\n多条件贝叶斯与后续6期号码比对结果：")
    multi_best_match = None
    multi_max_hits = -1

    for period_info in standard_periods:
        period_num = period_info['period']
        actual_numbers = period_info['numbers']

        # 计算多条件贝叶斯预测的命中数
        multi_hit_info = analyzer.check_hit_rate(multi_prediction, actual_numbers)
        multi_hits = multi_hit_info['total_hits']

        # 格式化实际号码显示
        formatted_numbers = analyzer.format_lottery_numbers(actual_numbers)
        print(f"第 {period_num} 期：实际号码 {formatted_numbers}，多条件贝叶斯命中 {multi_hits} 个")

        # 更新最佳匹配
        if multi_hits > multi_max_hits or (multi_hits == multi_max_hits and (multi_best_match is None or period_num < multi_best_match['period'])):
            multi_max_hits = multi_hits
            multi_best_match = {
                'period': period_num,
                'numbers': actual_numbers,
                'hits': multi_hits,
                'hit_info': multi_hit_info
            }

    print(f"\n多条件贝叶斯最佳匹配结果：")
    print(f"期号：{multi_best_match['period']}")
    formatted_multi_best_numbers = analyzer.format_lottery_numbers(multi_best_match['numbers'])
    print(f"实际号码：{formatted_multi_best_numbers}")
    print(f"命中数量：{multi_best_match['hits']} 个")
    print(f"命中详情：红球 {multi_best_match['hit_info']['red_hits']} 个，蓝球 {multi_best_match['hit_info']['blue_hits']} 个")

    # 与后续6期进行比对 - 全条件贝叶斯
    print(f"\n全条件贝叶斯与后续6期号码比对结果：")
    full_best_match = None
    full_max_hits = -1

    for period_info in standard_periods:
        period_num = period_info['period']
        actual_numbers = period_info['numbers']

        # 计算全条件贝叶斯预测的命中数
        full_hit_info = analyzer.check_hit_rate(full_prediction, actual_numbers)
        full_hits = full_hit_info['total_hits']

        # 格式化实际号码显示
        formatted_numbers = analyzer.format_lottery_numbers(actual_numbers)
        print(f"第 {period_num} 期：实际号码 {formatted_numbers}，全条件贝叶斯命中 {full_hits} 个")

        # 更新最佳匹配
        if full_hits > full_max_hits or (full_hits == full_max_hits and (full_best_match is None or period_num < full_best_match['period'])):
            full_max_hits = full_hits
            full_best_match = {
                'period': period_num,
                'numbers': actual_numbers,
                'hits': full_hits,
                'hit_info': full_hit_info
            }

    print(f"\n全条件贝叶斯最佳匹配结果：")
    print(f"期号：{full_best_match['period']}")
    formatted_full_best_numbers = analyzer.format_lottery_numbers(full_best_match['numbers'])
    print(f"实际号码：{formatted_full_best_numbers}")
    print(f"命中数量：{full_best_match['hits']} 个")
    print(f"命中详情：红球 {full_best_match['hit_info']['red_hits']} 个，蓝球 {full_best_match['hit_info']['blue_hits']} 个")

    # 与后续6期进行比对 - 多注预测
    print(f"\n多注预测与后续6期号码比对结果：")
    multi_pred_best_match = None
    multi_pred_max_hits = -1

    # 用于命中率计算的完整号码列表（按标准格式）
    if analyzer.lottery_type == "SSQ":
        # 对于命中率计算，只取前6个红球和前1个蓝球
        multi_pred_numbers = multi_pred_red[:6] + multi_pred_blue[:1]
    else:
        # 对于命中率计算，只取前5个红球和前2个蓝球
        multi_pred_numbers = multi_pred_red[:5] + multi_pred_blue[:2]

    for period_info in standard_periods:
        period_num = period_info['period']
        actual_numbers = period_info['numbers']

        # 计算多注预测的命中数
        multi_pred_hit_info = analyzer.check_hit_rate(multi_pred_numbers, actual_numbers)
        multi_pred_hits = multi_pred_hit_info['total_hits']

        # 格式化实际号码显示
        formatted_numbers = analyzer.format_lottery_numbers(actual_numbers)
        print(f"第 {period_num} 期：实际号码 {formatted_numbers}，多注预测命中 {multi_pred_hits} 个")

        # 更新最佳匹配
        if multi_pred_hits > multi_pred_max_hits or (multi_pred_hits == multi_pred_max_hits and (multi_pred_best_match is None or period_num < multi_pred_best_match['period'])):
            multi_pred_max_hits = multi_pred_hits
            multi_pred_best_match = {
                'period': period_num,
                'numbers': actual_numbers,
                'hits': multi_pred_hits,
                'hit_info': multi_pred_hit_info
            }

    print(f"\n多注预测最佳匹配结果：")
    print(f"期号：{multi_pred_best_match['period']}")
    formatted_multi_pred_best_numbers = analyzer.format_lottery_numbers(multi_pred_best_match['numbers'])
    print(f"实际号码：{formatted_multi_pred_best_numbers}")
    print(f"命中数量：{multi_pred_best_match['hits']} 个")
    print(f"命中详情：红球 {multi_pred_best_match['hit_info']['red_hits']} 个，蓝球 {multi_pred_best_match['hit_info']['blue_hits']} 个")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_single_analysis()
